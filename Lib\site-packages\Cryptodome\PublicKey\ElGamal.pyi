from typing import Callable, <PERSON>, Tuple, Optional

__all__ = ['generate', 'construct', 'ElGamalKey']

RNG = Callable[[int], bytes]

def generate(bits: int, randfunc: RNG) -> ElGamalKey: ...
def construct(tup: Union[Tuple[int, int, int], Tuple[int, int, int, int]]) -> ElGamalKey: ...

class ElGamalKey(object):
    def __init__(self, randfunc: Optional[RNG]=None) -> None: ...
    def has_private(self) -> bool: ...
    def can_encrypt(self) -> bool: ...
    def can_sign(self) -> bool: ...
    def publickey(self) -> ElGamalKey: ...
    def __eq__(self, other: object) -> bool: ...
    def __ne__(self, other: object) -> bool: ...
    def __getstate__(self) -> None: ...
